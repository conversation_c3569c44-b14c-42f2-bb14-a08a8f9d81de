#!/usr/bin/env bash

# Kiro IDE Installation Script for NixOS
# This script helps install Kiro IDE once you have access

set -e

echo "🚀 Kiro IDE Installation Helper for NixOS"
echo "========================================="

# Check if user has access
echo ""
echo "⚠️  IMPORTANT: Kiro IDE is currently in preview and requires waitlist access."
echo "   If you haven't joined the waitlist yet, visit: https://kiro.dev/"
echo ""

read -p "Do you have access to Kiro IDE downloads? (y/N): " has_access

if [[ ! "$has_access" =~ ^[Yy]$ ]]; then
    echo ""
    echo "📝 To get access to Kiro IDE:"
    echo "   1. Visit https://kiro.dev/"
    echo "   2. Click 'Join Waitlist'"
    echo "   3. Wait for invitation email"
    echo "   4. Run this script again once you have access"
    echo ""
    echo "🔗 Opening Kiro website..."
    xdg-open "https://kiro.dev/" 2>/dev/null || echo "Please visit https://kiro.dev/ manually"
    exit 0
fi

echo ""
echo "📦 Installation Methods:"
echo "1. AppImage (Recommended - Easy to update)"
echo "2. Add to NixOS configuration (System-wide)"
echo "3. Manual download only"
echo ""

read -p "Choose installation method (1-3): " method

case $method in
    1)
        echo ""
        echo "📥 AppImage Installation"
        echo "======================="
        
        # Create applications directory
        mkdir -p ~/.local/share/applications
        mkdir -p ~/.local/bin
        
        echo "Please download the Linux Universal AppImage from:"
        echo "https://kiro.dev/downloads"
        echo ""
        read -p "Enter the path to the downloaded Kiro AppImage: " appimage_path
        
        if [[ ! -f "$appimage_path" ]]; then
            echo "❌ File not found: $appimage_path"
            exit 1
        fi
        
        # Make executable and move to local bin
        chmod +x "$appimage_path"
        cp "$appimage_path" ~/.local/bin/kiro.AppImage
        
        # Create desktop entry
        cat > ~/.local/share/applications/kiro.desktop << EOF
[Desktop Entry]
Name=Kiro IDE
Comment=AI IDE for prototype to production
Exec=$HOME/.local/bin/kiro.AppImage
Icon=kiro
Terminal=false
Type=Application
Categories=Development;IDE;
StartupWMClass=Kiro
EOF
        
        echo "✅ Kiro IDE installed successfully!"
        echo "   You can now launch it from your application menu or run: ~/.local/bin/kiro.AppImage"
        ;;
        
    2)
        echo ""
        echo "🔧 NixOS Configuration Method"
        echo "============================="
        echo ""
        echo "To add Kiro IDE to your NixOS configuration:"
        echo ""
        echo "1. Download the AppImage from https://kiro.dev/downloads"
        echo "2. Calculate the SHA256 hash:"
        echo "   nix-hash --type sha256 --flat /path/to/kiro.AppImage"
        echo ""
        echo "3. Edit your configuration.nix and uncomment the Kiro section around line 256"
        echo "4. Replace the URL and add the calculated hash"
        echo "5. Run: sudo nixos-rebuild switch"
        echo ""
        echo "The configuration template is already prepared in your configuration.nix file."
        ;;
        
    3)
        echo ""
        echo "📁 Manual Download"
        echo "=================="
        echo "Opening Kiro downloads page..."
        xdg-open "https://kiro.dev/downloads" 2>/dev/null || echo "Please visit https://kiro.dev/downloads manually"
        echo ""
        echo "Download the 'Linux Universal' version for best compatibility with NixOS."
        ;;
        
    *)
        echo "❌ Invalid option selected"
        exit 1
        ;;
esac

echo ""
echo "🎉 Installation helper completed!"
echo ""
echo "📚 Additional Resources:"
echo "   • Documentation: https://kiro.dev/docs"
echo "   • Getting Started: https://kiro.dev/docs/getting-started/first-project/"
echo "   • Migration from VS Code: https://kiro.dev/docs/guides/migrating-from-vscode/"
